{"version": 3, "file": "mkdir.js", "sourceRoot": "", "sources": ["../../src/mkdir.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAA;AAC3C,OAAO,EAAE,MAAM,SAAS,CAAA;AACxB,OAAO,GAAG,MAAM,kBAAkB,CAAA;AAClC,OAAO,IAAI,MAAM,WAAW,CAAA;AAC5B,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAA;AACzC,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAA;AAClE,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAA;AAmBjD,MAAM,QAAQ,GAAG,CACf,GAAW,EACX,EAAmC,EACnC,EAAE;IACF,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;QACtB,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;YAC5B,EAAE,GAAG,IAAI,QAAQ,CACf,GAAG,EACF,EAA4B,EAAE,IAAI,IAAI,SAAS,CACjD,CAAA;QACH,CAAC;QACD,EAAE,CAAC,EAAE,CAAC,CAAA;IACR,CAAC,CAAC,CAAA;AACJ,CAAC,CAAA;AAED;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,CACnB,GAAW,EACX,GAAiB,EACjB,EAAmD,EACnD,EAAE;IACF,GAAG,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAA;IAE/B,gDAAgD;IAChD,oCAAoC;IACpC,oBAAoB;IACpB,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,IAAI,CAAA;IAC/B,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,MAAM,CAAA;IAC9B,MAAM,SAAS,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAA;IAEtC,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;IACnB,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;IACnB,MAAM,OAAO,GACX,OAAO,GAAG,KAAK,QAAQ;QACvB,OAAO,GAAG,KAAK,QAAQ;QACvB,CAAC,GAAG,KAAK,GAAG,CAAC,UAAU,IAAI,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,CAAA;IAEpD,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAA;IAC7B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;IACzB,MAAM,GAAG,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAEzC,MAAM,IAAI,GAAG,CAAC,EAAsB,EAAE,OAAgB,EAAE,EAAE;QACxD,IAAI,EAAE,EAAE,CAAC;YACP,EAAE,CAAC,EAAE,CAAC,CAAA;QACR,CAAC;aAAM,CAAC;YACN,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;gBACvB,MAAM,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAC7B,IAAI,CAAC,EAA2B,CAAC,CAClC,CAAA;YACH,CAAC;iBAAM,IAAI,SAAS,EAAE,CAAC;gBACrB,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,CAAA;YACzB,CAAC;iBAAM,CAAC;gBACN,EAAE,EAAE,CAAA;YACN,CAAC;QACH,CAAC;IACH,CAAC,CAAA;IAED,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;QAChB,OAAO,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CACnD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,IAAI,SAAS,CAAC,EAAE,SAAS;QAChD,IAAI,CACL,CAAA;IACH,CAAC;IAED,MAAM,GAAG,GAAG,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;IACzD,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC5B,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;AACxD,CAAC,CAAA;AAED,MAAM,MAAM,GAAG,CACb,IAAY,EACZ,KAAe,EACf,IAAY,EACZ,MAAe,EACf,GAAW,EACX,OAA2B,EAC3B,EAAmD,EAC7C,EAAE;IACR,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;QAClB,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC1B,CAAC;IACD,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;IACvB,MAAM,IAAI,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;IAC/D,EAAE,CAAC,KAAK,CACN,IAAI,EACJ,IAAI,EACJ,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,CACrD,CAAA;AACH,CAAC,CAAA;AAED,MAAM,OAAO,GACX,CACE,IAAY,EACZ,KAAe,EACf,IAAY,EACZ,MAAe,EACf,GAAW,EACX,OAA2B,EAC3B,EAAmD,EACnD,EAAE,CACJ,CAAC,EAAiC,EAAE,EAAE;IACpC,IAAI,EAAE,EAAE,CAAC;QACP,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;YAC5B,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,IAAI;oBACT,MAAM,CAAC,IAAI,IAAI,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;gBAClD,EAAE,CAAC,MAAM,CAAC,CAAA;YACZ,CAAC;iBAAM,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC5B,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA;YACrD,CAAC;iBAAM,IAAI,MAAM,EAAE,CAAC;gBAClB,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;oBACnB,IAAI,EAAE,EAAE,CAAC;wBACP,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;oBACf,CAAC;oBACD,EAAE,CAAC,KAAK,CACN,IAAI,EACJ,IAAI,EACJ,OAAO,CACL,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,MAAM,EACN,GAAG,EACH,OAAO,EACP,EAAE,CACH,CACF,CAAA;gBACH,CAAC,CAAC,CAAA;YACJ,CAAC;iBAAM,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC/B,OAAO,EAAE,CACP,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CACrD,CAAA;YACH,CAAC;iBAAM,CAAC;gBACN,EAAE,CAAC,EAAE,CAAC,CAAA;YACR,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,OAAO,IAAI,IAAI,CAAA;QACzB,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA;IACrD,CAAC;AACH,CAAC,CAAA;AAEH,MAAM,YAAY,GAAG,CAAC,GAAW,EAAE,EAAE;IACnC,IAAI,EAAE,GAAG,KAAK,CAAA;IACd,IAAI,IAAI,GAAuB,SAAS,CAAA;IACxC,IAAI,CAAC;QACH,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAA;IACrC,CAAC;IAAC,OAAO,EAAE,EAAE,CAAC;QACZ,IAAI,GAAI,EAA4B,EAAE,IAAI,CAAA;IAC5C,CAAC;YAAS,CAAC;QACT,IAAI,CAAC,EAAE,EAAE,CAAC;YACR,MAAM,IAAI,QAAQ,CAAC,GAAG,EAAE,IAAI,IAAI,SAAS,CAAC,CAAA;QAC5C,CAAC;IACH,CAAC;AACH,CAAC,CAAA;AAED,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,GAAiB,EAAE,EAAE;IAC1D,GAAG,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAA;IAC/B,gDAAgD;IAChD,oCAAoC;IACpC,oBAAoB;IACpB,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,IAAI,CAAA;IAC/B,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,KAAK,CAAA;IAC7B,MAAM,SAAS,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAA;IAEtC,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;IACnB,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAA;IACnB,MAAM,OAAO,GACX,OAAO,GAAG,KAAK,QAAQ;QACvB,OAAO,GAAG,KAAK,QAAQ;QACvB,CAAC,GAAG,KAAK,GAAG,CAAC,UAAU,IAAI,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,CAAA;IAEpD,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAA;IAC7B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAA;IACzB,MAAM,GAAG,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAEzC,MAAM,IAAI,GAAG,CAAC,OAA4B,EAAE,EAAE;QAC5C,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;YACvB,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;QAC/B,CAAC;QACD,IAAI,SAAS,EAAE,CAAC;YACd,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;QACzB,CAAC;IACH,CAAC,CAAA;IAED,IAAI,GAAG,KAAK,GAAG,EAAE,CAAC;QAChB,YAAY,CAAC,GAAG,CAAC,CAAA;QACjB,OAAO,IAAI,EAAE,CAAA;IACf,CAAC;IAED,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,IAAI,SAAS,CAAC,CAAA;IACxE,CAAC;IAED,MAAM,GAAG,GAAG,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;IACzD,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC5B,IAAI,OAAO,GAAuB,SAAS,CAAA;IAC3C,KACE,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,GAAG,GAAG,EACjC,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,EACtB,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,EACjB,CAAC;QACD,IAAI,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;QAE/C,IAAI,CAAC;YACH,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACxB,OAAO,GAAG,OAAO,IAAI,IAAI,CAAA;QAC3B,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,MAAM,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;YAC7B,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;gBACrB,SAAQ;YACV,CAAC;iBAAM,IAAI,MAAM,EAAE,CAAC;gBAClB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;gBACnB,EAAE,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBACxB,OAAO,GAAG,OAAO,IAAI,IAAI,CAAA;gBACzB,SAAQ;YACV,CAAC;iBAAM,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC/B,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;YAC7D,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC,OAAO,CAAC,CAAA;AACtB,CAAC,CAAA", "sourcesContent": ["import { chownr, chownrSync } from 'chownr'\nimport fs from 'node:fs'\nimport fsp from 'node:fs/promises'\nimport path from 'node:path'\nimport { CwdError } from './cwd-error.js'\nimport { normalizeWindowsPath } from './normalize-windows-path.js'\nimport { SymlinkError } from './symlink-error.js'\n\nexport type MkdirOptions = {\n  uid?: number\n  gid?: number\n  processUid?: number\n  processGid?: number\n  umask?: number\n  preserve: boolean\n  unlink: boolean\n  cwd: string\n  mode: number\n}\n\nexport type MkdirError =\n  | NodeJS.ErrnoException\n  | CwdError\n  | SymlinkError\n\nconst checkCwd = (\n  dir: string,\n  cb: (er?: null | MkdirError) => any,\n) => {\n  fs.stat(dir, (er, st) => {\n    if (er || !st.isDirectory()) {\n      er = new CwdError(\n        dir,\n        (er as NodeJS.ErrnoException)?.code || 'ENOTDIR',\n      )\n    }\n    cb(er)\n  })\n}\n\n/**\n * Wrapper around fs/promises.mkdir for tar's needs.\n *\n * The main purpose is to avoid creating directories if we know that\n * they already exist (and track which ones exist for this purpose),\n * and prevent entries from being extracted into symlinked folders,\n * if `preservePaths` is not set.\n */\nexport const mkdir = (\n  dir: string,\n  opt: MkdirOptions,\n  cb: (er?: null | MkdirError, made?: string) => void,\n) => {\n  dir = normalizeWindowsPath(dir)\n\n  // if there's any overlap between mask and mode,\n  // then we'll need an explicit chmod\n  /* c8 ignore next */\n  const umask = opt.umask ?? 0o22\n  const mode = opt.mode | 0o0700\n  const needChmod = (mode & umask) !== 0\n\n  const uid = opt.uid\n  const gid = opt.gid\n  const doChown =\n    typeof uid === 'number' &&\n    typeof gid === 'number' &&\n    (uid !== opt.processUid || gid !== opt.processGid)\n\n  const preserve = opt.preserve\n  const unlink = opt.unlink\n  const cwd = normalizeWindowsPath(opt.cwd)\n\n  const done = (er?: null | MkdirError, created?: string) => {\n    if (er) {\n      cb(er)\n    } else {\n      if (created && doChown) {\n        chownr(created, uid, gid, er =>\n          done(er as NodeJS.ErrnoException),\n        )\n      } else if (needChmod) {\n        fs.chmod(dir, mode, cb)\n      } else {\n        cb()\n      }\n    }\n  }\n\n  if (dir === cwd) {\n    return checkCwd(dir, done)\n  }\n\n  if (preserve) {\n    return fsp.mkdir(dir, { mode, recursive: true }).then(\n      made => done(null, made ?? undefined), // oh, ts\n      done,\n    )\n  }\n\n  const sub = normalizeWindowsPath(path.relative(cwd, dir))\n  const parts = sub.split('/')\n  mkdir_(cwd, parts, mode, unlink, cwd, undefined, done)\n}\n\nconst mkdir_ = (\n  base: string,\n  parts: string[],\n  mode: number,\n  unlink: boolean,\n  cwd: string,\n  created: string | undefined,\n  cb: (er?: null | MkdirError, made?: string) => void,\n): void => {\n  if (!parts.length) {\n    return cb(null, created)\n  }\n  const p = parts.shift()\n  const part = normalizeWindowsPath(path.resolve(base + '/' + p))\n  fs.mkdir(\n    part,\n    mode,\n    onmkdir(part, parts, mode, unlink, cwd, created, cb),\n  )\n}\n\nconst onmkdir =\n  (\n    part: string,\n    parts: string[],\n    mode: number,\n    unlink: boolean,\n    cwd: string,\n    created: string | undefined,\n    cb: (er?: null | MkdirError, made?: string) => void,\n  ) =>\n  (er?: null | NodeJS.ErrnoException) => {\n    if (er) {\n      fs.lstat(part, (statEr, st) => {\n        if (statEr) {\n          statEr.path =\n            statEr.path && normalizeWindowsPath(statEr.path)\n          cb(statEr)\n        } else if (st.isDirectory()) {\n          mkdir_(part, parts, mode, unlink, cwd, created, cb)\n        } else if (unlink) {\n          fs.unlink(part, er => {\n            if (er) {\n              return cb(er)\n            }\n            fs.mkdir(\n              part,\n              mode,\n              onmkdir(\n                part,\n                parts,\n                mode,\n                unlink,\n                cwd,\n                created,\n                cb,\n              ),\n            )\n          })\n        } else if (st.isSymbolicLink()) {\n          return cb(\n            new SymlinkError(part, part + '/' + parts.join('/')),\n          )\n        } else {\n          cb(er)\n        }\n      })\n    } else {\n      created = created || part\n      mkdir_(part, parts, mode, unlink, cwd, created, cb)\n    }\n  }\n\nconst checkCwdSync = (dir: string) => {\n  let ok = false\n  let code: string | undefined = undefined\n  try {\n    ok = fs.statSync(dir).isDirectory()\n  } catch (er) {\n    code = (er as NodeJS.ErrnoException)?.code\n  } finally {\n    if (!ok) {\n      throw new CwdError(dir, code ?? 'ENOTDIR')\n    }\n  }\n}\n\nexport const mkdirSync = (dir: string, opt: MkdirOptions) => {\n  dir = normalizeWindowsPath(dir)\n  // if there's any overlap between mask and mode,\n  // then we'll need an explicit chmod\n  /* c8 ignore next */\n  const umask = opt.umask ?? 0o22\n  const mode = opt.mode | 0o700\n  const needChmod = (mode & umask) !== 0\n\n  const uid = opt.uid\n  const gid = opt.gid\n  const doChown =\n    typeof uid === 'number' &&\n    typeof gid === 'number' &&\n    (uid !== opt.processUid || gid !== opt.processGid)\n\n  const preserve = opt.preserve\n  const unlink = opt.unlink\n  const cwd = normalizeWindowsPath(opt.cwd)\n\n  const done = (created?: string | undefined) => {\n    if (created && doChown) {\n      chownrSync(created, uid, gid)\n    }\n    if (needChmod) {\n      fs.chmodSync(dir, mode)\n    }\n  }\n\n  if (dir === cwd) {\n    checkCwdSync(cwd)\n    return done()\n  }\n\n  if (preserve) {\n    return done(fs.mkdirSync(dir, { mode, recursive: true }) ?? undefined)\n  }\n\n  const sub = normalizeWindowsPath(path.relative(cwd, dir))\n  const parts = sub.split('/')\n  let created: string | undefined = undefined\n  for (\n    let p = parts.shift(), part = cwd;\n    p && (part += '/' + p);\n    p = parts.shift()\n  ) {\n    part = normalizeWindowsPath(path.resolve(part))\n\n    try {\n      fs.mkdirSync(part, mode)\n      created = created || part\n    } catch (er) {\n      const st = fs.lstatSync(part)\n      if (st.isDirectory()) {\n        continue\n      } else if (unlink) {\n        fs.unlinkSync(part)\n        fs.mkdirSync(part, mode)\n        created = created || part\n        continue\n      } else if (st.isSymbolicLink()) {\n        return new SymlinkError(part, part + '/' + parts.join('/'))\n      }\n    }\n  }\n\n  return done(created)\n}\n"]}