// prettier-ignore
/* eslint-disable */
// @ts-nocheck
/* auto-generated by NAPI-RS */

const { createRequire } = require('node:module')
require = createRequire(__filename)

const { readFileSync } = require('node:fs')
let nativeBinding = null
const loadErrors = []

const isMusl = () => {
  let musl = false
  if (process.platform === 'linux') {
    musl = isMuslFromFilesystem()
    if (musl === null) {
      musl = isMuslFromReport()
    }
    if (musl === null) {
      musl = isMuslFromChildProcess()
    }
  }
  return musl
}

const isFileMusl = (f) => f.includes('libc.musl-') || f.includes('ld-musl-')

const isMuslFromFilesystem = () => {
  try {
    return readFileSync('/usr/bin/ldd', 'utf-8').includes('musl')
  } catch {
    return null
  }
}

const isMuslFromReport = () => {
  let report = null
  if (typeof process.report?.getReport === 'function') {
    process.report.excludeNetwork = true
    report = process.report.getReport()
  }
  if (!report) {
    return null
  }
  if (report.header && report.header.glibcVersionRuntime) {
    return false
  }
  if (Array.isArray(report.sharedObjects)) {
    if (report.sharedObjects.some(isFileMusl)) {
      return true
    }
  }
  return false
}

const isMuslFromChildProcess = () => {
  try {
    return require('child_process').execSync('ldd --version', { encoding: 'utf8' }).includes('musl')
  } catch (e) {
    // If we reach this case, we don't know if the system is musl or not, so is better to just fallback to false
    return false
  }
}

function requireNative() {
  if (process.env.NAPI_RS_NATIVE_LIBRARY_PATH) {
    try {
      return require(process.env.NAPI_RS_NATIVE_LIBRARY_PATH);
    } catch (err) {
      loadErrors.push(err)
    }
  } else if (process.platform === 'android') {
    if (process.arch === 'arm64') {
      try {
        return require('./tailwindcss-oxide.android-arm64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-android-arm64')
        const bindingPackageVersion = require('@tailwindcss/oxide-android-arm64/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'arm') {
      try {
        return require('./tailwindcss-oxide.android-arm-eabi.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-android-arm-eabi')
        const bindingPackageVersion = require('@tailwindcss/oxide-android-arm-eabi/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on Android ${process.arch}`))
    }
  } else if (process.platform === 'win32') {
    if (process.arch === 'x64') {
      try {
        return require('./tailwindcss-oxide.win32-x64-msvc.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-win32-x64-msvc')
        const bindingPackageVersion = require('@tailwindcss/oxide-win32-x64-msvc/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'ia32') {
      try {
        return require('./tailwindcss-oxide.win32-ia32-msvc.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-win32-ia32-msvc')
        const bindingPackageVersion = require('@tailwindcss/oxide-win32-ia32-msvc/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'arm64') {
      try {
        return require('./tailwindcss-oxide.win32-arm64-msvc.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-win32-arm64-msvc')
        const bindingPackageVersion = require('@tailwindcss/oxide-win32-arm64-msvc/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on Windows: ${process.arch}`))
    }
  } else if (process.platform === 'darwin') {
    try {
      return require('./tailwindcss-oxide.darwin-universal.node')
    } catch (e) {
      loadErrors.push(e)
    }
    try {
      const binding = require('@tailwindcss/oxide-darwin-universal')
      const bindingPackageVersion = require('@tailwindcss/oxide-darwin-universal/package.json').version
      if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
        throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
      }
      return binding
    } catch (e) {
      loadErrors.push(e)
    }
    if (process.arch === 'x64') {
      try {
        return require('./tailwindcss-oxide.darwin-x64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-darwin-x64')
        const bindingPackageVersion = require('@tailwindcss/oxide-darwin-x64/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'arm64') {
      try {
        return require('./tailwindcss-oxide.darwin-arm64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-darwin-arm64')
        const bindingPackageVersion = require('@tailwindcss/oxide-darwin-arm64/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on macOS: ${process.arch}`))
    }
  } else if (process.platform === 'freebsd') {
    if (process.arch === 'x64') {
      try {
        return require('./tailwindcss-oxide.freebsd-x64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-freebsd-x64')
        const bindingPackageVersion = require('@tailwindcss/oxide-freebsd-x64/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'arm64') {
      try {
        return require('./tailwindcss-oxide.freebsd-arm64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-freebsd-arm64')
        const bindingPackageVersion = require('@tailwindcss/oxide-freebsd-arm64/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on FreeBSD: ${process.arch}`))
    }
  } else if (process.platform === 'linux') {
    if (process.arch === 'x64') {
      if (isMusl()) {
        try {
          return require('./tailwindcss-oxide.linux-x64-musl.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-x64-musl')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-x64-musl/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      } else {
        try {
          return require('./tailwindcss-oxide.linux-x64-gnu.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-x64-gnu')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-x64-gnu/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      }
    } else if (process.arch === 'arm64') {
      if (isMusl()) {
        try {
          return require('./tailwindcss-oxide.linux-arm64-musl.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-arm64-musl')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-arm64-musl/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      } else {
        try {
          return require('./tailwindcss-oxide.linux-arm64-gnu.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-arm64-gnu')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-arm64-gnu/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      }
    } else if (process.arch === 'arm') {
      if (isMusl()) {
        try {
          return require('./tailwindcss-oxide.linux-arm-musleabihf.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-arm-musleabihf')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-arm-musleabihf/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      } else {
        try {
          return require('./tailwindcss-oxide.linux-arm-gnueabihf.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-arm-gnueabihf')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-arm-gnueabihf/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      }
    } else if (process.arch === 'loong64') {
      if (isMusl()) {
        try {
          return require('./tailwindcss-oxide.linux-loong64-musl.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-loong64-musl')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-loong64-musl/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      } else {
        try {
          return require('./tailwindcss-oxide.linux-loong64-gnu.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-loong64-gnu')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-loong64-gnu/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      }
    } else if (process.arch === 'riscv64') {
      if (isMusl()) {
        try {
          return require('./tailwindcss-oxide.linux-riscv64-musl.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-riscv64-musl')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-riscv64-musl/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      } else {
        try {
          return require('./tailwindcss-oxide.linux-riscv64-gnu.node')
        } catch (e) {
          loadErrors.push(e)
        }
        try {
          const binding = require('@tailwindcss/oxide-linux-riscv64-gnu')
          const bindingPackageVersion = require('@tailwindcss/oxide-linux-riscv64-gnu/package.json').version
          if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
            throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
          }
          return binding
        } catch (e) {
          loadErrors.push(e)
        }
      }
    } else if (process.arch === 'ppc64') {
      try {
        return require('./tailwindcss-oxide.linux-ppc64-gnu.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-linux-ppc64-gnu')
        const bindingPackageVersion = require('@tailwindcss/oxide-linux-ppc64-gnu/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 's390x') {
      try {
        return require('./tailwindcss-oxide.linux-s390x-gnu.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-linux-s390x-gnu')
        const bindingPackageVersion = require('@tailwindcss/oxide-linux-s390x-gnu/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on Linux: ${process.arch}`))
    }
  } else if (process.platform === 'openharmony') {
    if (process.arch === 'arm64') {
      try {
        return require('./tailwindcss-oxide.openharmony-arm64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-openharmony-arm64')
        const bindingPackageVersion = require('@tailwindcss/oxide-openharmony-arm64/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'x64') {
      try {
        return require('./tailwindcss-oxide.openharmony-x64.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-openharmony-x64')
        const bindingPackageVersion = require('@tailwindcss/oxide-openharmony-x64/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else if (process.arch === 'arm') {
      try {
        return require('./tailwindcss-oxide.openharmony-arm.node')
      } catch (e) {
        loadErrors.push(e)
      }
      try {
        const binding = require('@tailwindcss/oxide-openharmony-arm')
        const bindingPackageVersion = require('@tailwindcss/oxide-openharmony-arm/package.json').version
        if (bindingPackageVersion !== '4.1.14' && process.env.NAPI_RS_ENFORCE_VERSION_CHECK && process.env.NAPI_RS_ENFORCE_VERSION_CHECK !== '0') {
          throw new Error(`Native binding package version mismatch, expected 4.1.14 but got ${bindingPackageVersion}. You can reinstall dependencies to fix this issue.`)
        }
        return binding
      } catch (e) {
        loadErrors.push(e)
      }
    } else {
      loadErrors.push(new Error(`Unsupported architecture on OpenHarmony: ${process.arch}`))
    }
  } else {
    loadErrors.push(new Error(`Unsupported OS: ${process.platform}, architecture: ${process.arch}`))
  }
}

nativeBinding = requireNative()

if (!nativeBinding || process.env.NAPI_RS_FORCE_WASI) {
  let wasiBinding = null
  let wasiBindingError = null
  try {
    wasiBinding = require('./tailwindcss-oxide.wasi.cjs')
    nativeBinding = wasiBinding
  } catch (err) {
    if (process.env.NAPI_RS_FORCE_WASI) {
      wasiBindingError = err
    }
  }
  if (!nativeBinding) {
    try {
      wasiBinding = require('@tailwindcss/oxide-wasm32-wasi')
      nativeBinding = wasiBinding
    } catch (err) {
      if (process.env.NAPI_RS_FORCE_WASI) {
        wasiBindingError.cause = err
        loadErrors.push(err)
      }
    }
  }
  if (process.env.NAPI_RS_FORCE_WASI === 'error' && !wasiBinding) {
    const error = new Error('WASI binding not found and NAPI_RS_FORCE_WASI is set to error')
    error.cause = wasiBindingError
    throw error
  }
}

if (!nativeBinding) {
  if (loadErrors.length > 0) {
    throw new Error(
      `Cannot find native binding. ` +
        `npm has a bug related to optional dependencies (https://github.com/npm/cli/issues/4828). ` +
        'Please try `npm i` again after removing both package-lock.json and node_modules directory.',
      {
        cause: loadErrors.reduce((err, cur) => {
          cur.cause = err
          return cur
        }),
      },
    )
  }
  throw new Error(`Failed to load native binding`)
}

module.exports = nativeBinding
module.exports.Scanner = nativeBinding.Scanner
