{"version": 3, "file": "normalize-unicode.js", "sourceRoot": "", "sources": ["../../src/normalize-unicode.ts"], "names": [], "mappings": ";;;AAAA,oCAAoC;AACpC,+CAA+C;AAC/C,6CAA6C;AAC7C,4CAA4C;AAC5C,MAAM,cAAc,GAA2B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;AAElE,4DAA4D;AAC5D,MAAM,GAAG,GAAG,KAAK,CAAA;AACjB,MAAM,KAAK,GAAG,IAAI,GAAG,EAAU,CAAA;AACxB,MAAM,gBAAgB,GAAG,CAAC,CAAS,EAAU,EAAE;IACpD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAClB,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;IACxC,CAAC;SAAM,CAAC;QACN,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;IACjB,CAAC;IACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IAEZ,MAAM,GAAG,GAAG,cAAc,CAAC,CAAC,CAAW,CAAA;IAEvC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,GAAG,CAAA;IACxB,yCAAyC;IACzC,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;QACjB,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC;YACtB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YACf,OAAO,cAAc,CAAC,CAAC,CAAC,CAAA;YACxB,IAAI,EAAE,CAAC,IAAI,CAAC;gBAAE,MAAK;QACrB,CAAC;IACH,CAAC;IAED,OAAO,GAAG,CAAA;AACZ,CAAC,CAAA;AArBY,QAAA,gBAAgB,oBAqB5B", "sourcesContent": ["// warning: extremely hot code path.\n// This has been meticulously optimized for use\n// within npm install on large package trees.\n// Do not edit without careful benchmarking.\nconst normalizeCache: Record<string, string> = Object.create(null)\n\n// Limit the size of this. Very low-sophistication LRU cache\nconst MAX = 10000\nconst cache = new Set<string>()\nexport const normalizeUnicode = (s: string): string => {\n  if (!cache.has(s)) {\n    normalizeCache[s] = s.normalize('NFD')\n  } else {\n    cache.delete(s)\n  }\n  cache.add(s)\n\n  const ret = normalizeCache[s] as string\n\n  let i = cache.size - MAX\n  // only prune when we're 10% over the max\n  if (i > MAX / 10) {\n    for (const s of cache) {\n      cache.delete(s)\n      delete normalizeCache[s]\n      if (--i <= 0) break\n    }\n  }\n\n  return ret\n}\n"]}